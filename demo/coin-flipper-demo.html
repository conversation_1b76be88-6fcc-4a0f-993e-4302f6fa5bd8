<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🪙 CoinFlipper <PERSON><PERSON>le <PERSON></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .coin-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .coin-canvas {
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }

        .status-info h3 {
            margin-top: 0;
            color: #007bff;
        }

        .status-info p {
            margin: 8px 0;
            font-weight: 500;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .control-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .control-group h4 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 8px;
            min-width: 120px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .betting-section {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
        }

        .bet-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bet-input input, .bet-input select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }

        .bet-input input:focus, .bet-input select:focus {
            outline: none;
            border-color: #007bff;
        }

        .result-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
        }

        .result-win {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-color: #28a745;
            color: #155724;
        }

        .result-lose {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: #dc3545;
            color: #721c24;
        }

        .game-history {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 14px;
        }

        .history-item.win {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .history-item.lose {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .coin-canvas {
                width: 300px;
                height: 300px;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
            
            .betting-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn {
                width: 100%;
                margin: 5px 0;
            }
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 CoinFlipper Module Demo</h1>
        
        <div id="loading" class="loading">
            🔄 Loading CoinFlipper Module...
        </div>

        <div id="error" class="error" style="display: none;">
            ❌ Failed to initialize CoinFlipper. Please check console for details.
        </div>

        <div id="main-content" style="display: none;">
            <!-- Canvas สำหรับแสดงเหรียญ 3D -->
            <div class="coin-container">
                <canvas 
                    id="coinCanvas"
                    width="400" 
                    height="400"
                    class="coin-canvas"
                ></canvas>
            </div>

            <!-- ข้อมูลสถานะ -->
            <div class="status-info">
                <h3>📊 Status Information</h3>
                <p><strong>Module Status:</strong> <span id="moduleStatus">Ready</span></p>
                <p><strong>Last Result:</strong> <span id="lastResult">None</span></p>
                <p><strong>Balance:</strong> $<span id="balance">1000</span></p>
                <p><strong>Games Played:</strong> <span id="gamesPlayed">0</span></p>
            </div>

            <!-- ปุ่มควบคุม -->
            <div class="controls">
                <!-- Idle Controls -->
                <div class="control-group">
                    <h4>🔄 Idle Animation</h4>
                    <button id="startIdle" class="btn btn-secondary">
                        ▶️ Start Idle
                    </button>
                    <button id="stopIdle" class="btn btn-secondary">
                        ⏹️ Stop Idle
                    </button>
                </div>

                <!-- Betting Controls -->
                <div class="control-group">
                    <h4>🎲 Betting Game</h4>
                    <div class="betting-section">
                        <div class="bet-input">
                            <label>Bet: $</label>
                            <input id="betAmount" type="number" min="1" max="1000" value="50">
                        </div>
                        <div class="bet-input">
                            <label>Choose:</label>
                            <select id="betChoice">
                                <option value="heads">👑 Heads</option>
                                <option value="tails">🦅 Tails</option>
                            </select>
                        </div>
                    </div>
                    <button id="placeBet" class="btn btn-primary">
                        🎯 Place Bet & Flip!
                    </button>
                </div>

                <!-- Test Controls -->
                <div class="control-group">
                    <h4>🧪 Test Controls</h4>
                    <button id="testHeads" class="btn btn-success">
                        👑 Test Heads
                    </button>
                    <button id="testTails" class="btn btn-success">
                        🦅 Test Tails
                    </button>
                    <button id="testRandom" class="btn btn-info">
                        🎲 Test Random
                    </button>
                </div>

                <!-- Sound Controls -->
                <div class="control-group">
                    <h4>🔊 Sound Effects</h4>
                    <button id="playWinSound" class="btn btn-warning">
                        🎉 Win Sound
                    </button>
                    <button id="playLoseSound" class="btn btn-warning">
                        😔 Lose Sound
                    </button>
                </div>
            </div>

            <!-- ผลลัพธ์ -->
            <div id="resultDisplay" class="result-display">
                Ready to flip! 🪙
            </div>

            <!-- ประวัติการเล่น -->
            <div class="game-history">
                <h4>📈 Game History (Last 10 games)</h4>
                <div id="historyList">
                    <p style="text-align: center; color: #666;">No games played yet</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- Base64 coin images -->
    <script src="../src/coin_base64.js"></script>
    <!-- CoinFlipper Module -->
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        // Global variables
        let coinFlipper = null;
        let balance = 1000;
        let gamesPlayed = 0;
        let gameHistory = [];
        let statusInterval = null;

        // DOM elements
        const elements = {
            loading: document.getElementById('loading'),
            error: document.getElementById('error'),
            mainContent: document.getElementById('main-content'),
            moduleStatus: document.getElementById('moduleStatus'),
            lastResult: document.getElementById('lastResult'),
            balanceDisplay: document.getElementById('balance'),
            gamesPlayedDisplay: document.getElementById('gamesPlayed'),
            betAmount: document.getElementById('betAmount'),
            betChoice: document.getElementById('betChoice'),
            resultDisplay: document.getElementById('resultDisplay'),
            historyList: document.getElementById('historyList')
        };

        // Initialize the application
        async function init() {
            try {
                elements.loading.style.display = 'block';

                // Show main content first so canvas is visible
                elements.mainContent.style.display = 'block';

                // Create CoinFlipper instance
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });

                // Wait for ready
                await coinFlipper.ready();

                // Start idle animation
                await coinFlipper.startIdle();

                // Hide loading
                elements.loading.style.display = 'none';

                // Setup event listeners
                setupEventListeners();

                // Start status updates
                startStatusUpdates();

                // Update displays
                updateDisplays();

                console.log('✅ CoinFlipper initialized successfully');

            } catch (error) {
                console.error('❌ Failed to initialize CoinFlipper:', error);
                elements.loading.style.display = 'none';
                elements.error.style.display = 'block';
                elements.mainContent.style.display = 'none';
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Idle controls
            document.getElementById('startIdle').onclick = async () => {
                try {
                    await coinFlipper.startIdle();
                } catch (error) {
                    console.error('Failed to start idle:', error);
                }
            };

            document.getElementById('stopIdle').onclick = async () => {
                try {
                    await coinFlipper.stopIdle();
                } catch (error) {
                    console.error('Failed to stop idle:', error);
                }
            };

            // Betting
            document.getElementById('placeBet').onclick = placeBet;

            // Test controls
            document.getElementById('testHeads').onclick = () => testFlip('heads');
            document.getElementById('testTails').onclick = () => testFlip('tails');
            document.getElementById('testRandom').onclick = () => testFlip();

            // Sound controls
            document.getElementById('playWinSound').onclick = async () => {
                try {
                    await coinFlipper.playWinSound();
                } catch (error) {
                    console.error('Failed to play win sound:', error);
                }
            };

            document.getElementById('playLoseSound').onclick = async () => {
                try {
                    await coinFlipper.playLoseSound();
                } catch (error) {
                    console.error('Failed to play lose sound:', error);
                }
            };

            // Bet amount validation
            elements.betAmount.oninput = () => {
                const value = parseInt(elements.betAmount.value);
                if (value > balance) {
                    elements.betAmount.value = balance;
                } else if (value < 1) {
                    elements.betAmount.value = 1;
                }
            };
        }

        // Place bet and flip
        async function placeBet() {
            const betAmount = parseInt(elements.betAmount.value);
            const betChoice = elements.betChoice.value;

            if (betAmount > balance || betAmount < 1) {
                alert('Invalid bet amount!');
                return;
            }

            try {
                elements.resultDisplay.textContent = '🎲 Flipping...';
                elements.resultDisplay.className = 'result-display';

                // Stop idle and flip
                await coinFlipper.stopIdle();
                const result = await coinFlipper.toss();

                // Check win/lose
                const won = result === betChoice;

                // Update balance
                if (won) {
                    balance += betAmount;
                    await coinFlipper.playWinSound();
                    elements.resultDisplay.textContent = `🎉 You Won! Result: ${result}`;
                    elements.resultDisplay.className = 'result-display result-win';
                } else {
                    balance -= betAmount;
                    await coinFlipper.playLoseSound();
                    elements.resultDisplay.textContent = `😔 You Lost! Result: ${result}`;
                    elements.resultDisplay.className = 'result-display result-lose';
                }

                // Update game history
                gameHistory.unshift({
                    choice: betChoice,
                    result: result,
                    amount: betAmount,
                    won: won,
                    timestamp: new Date()
                });

                // Keep only last 10 games
                if (gameHistory.length > 10) {
                    gameHistory = gameHistory.slice(0, 10);
                }

                gamesPlayed++;
                elements.lastResult.textContent = result;
                updateDisplays();

                // Resume idle after delay
                setTimeout(async () => {
                    await coinFlipper.startIdle();
                }, 2000);

            } catch (error) {
                console.error('Failed to place bet:', error);
                elements.resultDisplay.textContent = '❌ Error occurred!';
                elements.resultDisplay.className = 'result-display';
            }
        }

        // Test flip
        async function testFlip(result = null) {
            try {
                elements.resultDisplay.textContent = '🧪 Testing...';
                elements.resultDisplay.className = 'result-display';

                await coinFlipper.stopIdle();
                const flipResult = await coinFlipper.toss(result);
                
                elements.resultDisplay.textContent = `🧪 Test Result: ${flipResult}`;
                elements.lastResult.textContent = flipResult;

                setTimeout(async () => {
                    await coinFlipper.startIdle();
                }, 1000);

            } catch (error) {
                console.error('Test failed:', error);
            }
        }

        // Start status updates
        function startStatusUpdates() {
            statusInterval = setInterval(() => {
                if (coinFlipper) {
                    const status = coinFlipper.status;
                    let statusText = 'Ready';
                    
                    if (!status.isInitialized) statusText = 'Initializing...';
                    else if (status.isFlipping) statusText = 'Flipping...';
                    else if (status.isIdle) statusText = 'Idle (spinning)';
                    
                    elements.moduleStatus.textContent = statusText;
                }
            }, 100);
        }

        // Update displays
        function updateDisplays() {
            elements.balanceDisplay.textContent = balance;
            elements.gamesPlayedDisplay.textContent = gamesPlayed;
            elements.betAmount.max = balance;

            // Update history
            if (gameHistory.length === 0) {
                elements.historyList.innerHTML = '<p style="text-align: center; color: #666;">No games played yet</p>';
            } else {
                elements.historyList.innerHTML = gameHistory.map(game => `
                    <div class="history-item ${game.won ? 'win' : 'lose'}">
                        <span>${game.choice} vs ${game.result}</span>
                        <span>${game.won ? '+' : '-'}$${game.amount}</span>
                    </div>
                `).join('');
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
            if (coinFlipper) {
                coinFlipper.destroy();
            }
        });

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
