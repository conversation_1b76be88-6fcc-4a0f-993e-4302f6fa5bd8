/**
 * TypeScript definitions for CoinFlipper Module
 * สำหรับใช้งานใน Vue.js projects ที่ใช้ TypeScript
 */

declare module 'coin-flipper' {
    export interface CoinFlipperOptions {
        /** Auto-load Three.js from CDN (default: true) */
        autoLoadThreeJS?: boolean;
        /** Three.js CDN URL */
        threeJSCDN?: string;
        /** Idle animation speed (default: 0.02) */
        idleSpeed?: number;
        /** Flip animation duration in milliseconds (default: 2000) */
        flipDuration?: number;
        /** Enable sound effects (default: true) */
        enableSound?: boolean;
    }

    export interface CoinFlipperStatus {
        /** Whether the module is initialized */
        isInitialized: boolean;
        /** Whether audio manager is available */
        hasAudio: boolean;
        /** Whether coin renderer is available */
        hasRenderer: boolean;
        /** Whether coin is currently flipping */
        isFlipping: boolean;
        /** Whether idle animation is running */
        isIdle: boolean;
    }

    export type FlipResult = 'heads' | 'tails';

    export interface Vector3 {
        x: number;
        y: number;
        z: number;
    }

    /**
     * AudioManager Class - จัดการเสียงทั้งหมด
     */
    export class AudioManager {
        constructor();
        
        /** Initialize audio context */
        initAudioContext(): void;
        
        /** Resume audio context (required for user interaction) */
        resumeAudioContext(): void;
        
        /** Generate coin flip sound */
        generateFlipSound(): void;
        
        /** Generate win sound */
        generateWinSound(): void;
        
        /** Generate lose sound */
        generateLoseSound(): void;
    }

    export interface CoinRendererOptions {
        /** Idle animation speed */
        idleSpeed?: number;
        /** Flip animation duration */
        flipDuration?: number;
    }

    /**
     * CoinRenderer Class - จัดการ 3D animation ด้วย Three.js
     */
    export class CoinRenderer {
        constructor(canvasId: string | HTMLCanvasElement, options?: CoinRendererOptions);
        
        /** Whether coin is currently flipping */
        readonly isFlipping: boolean;
        
        /** Whether idle animation is running */
        readonly isIdle: boolean;
        
        /** Start idle animation (coin spinning slowly) */
        startIdle(): void;
        
        /** Stop idle animation */
        stopIdle(): void;
        
        /** 
         * Flip the coin
         * @param result - Force specific result ('heads' or 'tails'), or null for random
         * @returns Promise that resolves to the flip result
         */
        flipCoin(result?: FlipResult | null): Promise<FlipResult>;
        
        /** Destroy the renderer and clean up resources */
        destroy(): void;
        
        /** Resize the renderer to match canvas size */
        resize(): void;
    }

    /**
     * CoinFlipper Main Class - คลาสหลักสำหรับใช้งานใน Vue.js
     */
    export class CoinFlipper {
        constructor(canvasId: string | HTMLCanvasElement, options?: CoinFlipperOptions);
        
        /** Get current status */
        readonly status: CoinFlipperStatus;
        
        /** Wait for the module to be ready */
        ready(): Promise<void>;
        
        /** 
         * Start idle animation (coin spinning slowly)
         * เหรียญจะหมุนไปเรื่อยๆ ด้วยความเร็วที่กำหนดได้
         */
        startIdle(): Promise<void>;
        
        /** Stop idle animation */
        stopIdle(): Promise<void>;
        
        /** 
         * Toss the coin
         * การแสดงเหรียญกำลังถูกทอยพร้อมเสียง และกำหนดผลลัพธ์ของการทอยได้
         * @param result - Force specific result ('heads' or 'tails'), or null for random
         * @param playSound - Whether to play flip sound (default: true)
         * @returns Promise that resolves to the flip result
         */
        toss(result?: FlipResult | null, playSound?: boolean): Promise<FlipResult>;
        
        /** Play win sound effect */
        playWinSound(): Promise<void>;
        
        /** Play lose sound effect */
        playLoseSound(): Promise<void>;
        
        /** Resize the renderer to match canvas size */
        resize(): Promise<void>;
        
        /** Destroy the coin flipper and clean up all resources */
        destroy(): void;
    }

    // Default export
    export default CoinFlipper;
}

// Global declarations for browser usage
declare global {
    interface Window {
        CoinFlipper: typeof import('coin-flipper').CoinFlipper;
        CoinFlipperAudioManager: typeof import('coin-flipper').AudioManager;
        CoinFlipperRenderer: typeof import('coin-flipper').CoinRenderer;
    }
}

// Vue.js plugin declaration
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $coinFlipper: import('coin-flipper').CoinFlipper;
    }
}

export {};
